import React, { useState, useRef, useEffect, forwardRef, useCallback } from 'react';
import { DateTimePickerProps } from './types';
import { formatDate, parseDate, isDateDisabled } from './utils';
import { useTranslation } from 'react-i18next';
import Calendar from './Calendar';
import TimeScrollWheel from './TimeScrollWheel';
import { Icon } from '@/shared/components/common';
import {
  useFloating,
  autoUpdate,
  offset,
  flip,
  shift,
  useClick,
  useDismiss,
  useRole,
  useInteractions,
  FloatingPortal,
  FloatingFocusManager,
  Placement,
} from '@floating-ui/react';

/**
 * DateTimePicker component cho phép người dùng chọn cả ngày và giờ một cách trực quan.
 *
 * Component này kết hợp DatePicker với time selection, cung cấp giao diện thân thiện
 * để chọn ngày tháng và thời gian trong một component duy nhất.
 *
 * @example
 * ```tsx
 * import { DateTimePicker } from '@/shared/components/common';
 * import { useState } from 'react';
 *
 * const MyComponent = () => {
 *   const [dateTime, setDateTime] = useState<Date | null>(null);
 *
 *   return (
 *     <DateTimePicker
 *       label="Chọn ngày và giờ"
 *       value={dateTime}
 *       onChange={setDateTime}
 *       placeholder="DD/MM/YYYY HH:mm"
 *     />
 *   );
 * };
 * ```
 */
const DateTimePicker = forwardRef<HTMLInputElement, DateTimePickerProps>(
  (
    {
      value,
      onChange,
      format = 'dd/MM/yyyy HH:mm',
      placeholder,
      label,
      disabled = false,
      disabledDates,
      minDate,
      maxDate,
      clearable = true,
      placement = 'bottom',
      size = 'md',
      fullWidth = false,
      error,
      helperText,
      className = '',
      showCalendarIcon = true,
      autoClose = false,
      showToday = true,
      showWeekNumbers = false,
      firstDayOfWeek = 1,
      weekDayNames,
      monthNames,
      showTodayButton = false,
      todayButtonText,
      clearButtonText,
      onClose,
      inputRef,
      inputProps,
      calendarIcon,
      iconOnly = false,
      hiddenInput = false,
      noBorder = false,
      timeFormat = '24h',
      showSeconds = false,
      minuteStep = 1,
    }
  ) => {
    const { t } = useTranslation();

    const [isOpen, setIsOpen] = useState(false);
    const [inputValue, setInputValue] = useState('');
    const [calendarMonth, setCalendarMonth] = useState(value || new Date());
    const [selectedTime, setSelectedTime] = useState({
      hours: value ? value.getHours() : 9,
      minutes: value ? value.getMinutes() : 0,
      seconds: value ? value.getSeconds() : 0,
    });

    const internalInputRef = useRef<HTMLInputElement>(null);
    const finalInputRef = inputRef || internalInputRef;

    // Floating UI setup
    const { refs, floatingStyles, context } = useFloating({
      open: isOpen,
      onOpenChange: setIsOpen,
      middleware: [offset(5), flip(), shift()],
      whileElementsMounted: autoUpdate,
      placement: placement as Placement,
    });

    const click = useClick(context);
    const dismiss = useDismiss(context);
    const role = useRole(context);

    const { getReferenceProps, getFloatingProps } = useInteractions([
      click,
      dismiss,
      role,
    ]);

    // Format display value
    const formatDisplayValue = useCallback((date: Date | null | undefined) => {
      if (!date) return '';
      try {
        return formatDate(date, format);
      } catch {
        return date.toLocaleString('vi-VN');
      }
    }, [format]);

    // Update input value when value changes
    useEffect(() => {
      setInputValue(formatDisplayValue(value));
      if (value) {
        setCalendarMonth(value);
        setSelectedTime({
          hours: value.getHours(),
          minutes: value.getMinutes(),
          seconds: value.getSeconds(),
        });
      }
    }, [value, formatDisplayValue]);

    // Handle date selection from calendar
    const handleSelectDate = useCallback((date: Date) => {
      const newDateTime = new Date(date);
      newDateTime.setHours(selectedTime.hours, selectedTime.minutes, selectedTime.seconds);

      onChange?.(newDateTime);
      setInputValue(formatDisplayValue(newDateTime));

      if (autoClose) {
        setIsOpen(false);
        onClose?.();
      }
    }, [selectedTime, onChange, formatDisplayValue, autoClose, onClose]);

    // Handle time change
    const handleTimeChange = useCallback((timeType: 'hours' | 'minutes' | 'seconds', timeValue: number) => {
      const newTime = { ...selectedTime, [timeType]: timeValue };
      setSelectedTime(newTime);

      if (value) {
        const newDateTime = new Date(value);
        newDateTime.setHours(newTime.hours, newTime.minutes, newTime.seconds);
        onChange?.(newDateTime);
        setInputValue(formatDisplayValue(newDateTime));
      }
    }, [selectedTime, value, onChange, formatDisplayValue]);

    // Handle input change
    const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value;
      setInputValue(newValue);

      if (newValue.trim() === '') {
        onChange?.(null);
        return;
      }

      try {
        const parsedDate = parseDate(newValue, format);
        if (parsedDate && !isDateDisabled(parsedDate, disabledDates, minDate, maxDate)) {
          onChange?.(parsedDate);
          setCalendarMonth(parsedDate);
          setSelectedTime({
            hours: parsedDate.getHours(),
            minutes: parsedDate.getMinutes(),
            seconds: parsedDate.getSeconds(),
          });
        }
      } catch {
        // Invalid date format, keep input value but don't update date
      }
    }, [format, onChange, minDate, maxDate, disabledDates]);

    // Handle clear
    const handleClear = useCallback(() => {
      onChange?.(null);
      setInputValue('');
      setSelectedTime({ hours: 9, minutes: 0, seconds: 0 });
    }, [onChange]);

    // Handle close
    const handleClose = useCallback(() => {
      setIsOpen(false);
      onClose?.();
    }, [onClose]);

    // Month navigation
    const handleMonthChange = useCallback((newMonth: Date) => {
      setCalendarMonth(newMonth);
    }, []);

    // Size classes
    const sizeClasses = {
      sm: 'h-8 text-sm',
      md: 'h-10 text-sm',
      lg: 'h-12 text-base',
    };

    // Generate time options
    const hours = Array.from({ length: timeFormat === '12h' ? 12 : 24 }, (_, i) =>
      timeFormat === '12h' ? (i === 0 ? 12 : i) : i
    );
    const minutes = Array.from({ length: 60 / minuteStep }, (_, i) => i * minuteStep);
    const seconds = showSeconds ? Array.from({ length: 60 }, (_, i) => i) : [];

    return (
      <div className={`relative ${fullWidth ? 'w-full' : 'w-auto'} ${className}`}>
        {/* Label */}
        {label && !iconOnly && (
          <label className="block text-sm font-medium text-foreground mb-1">
            {label}
          </label>
        )}

        {/* Input */}
        <div
          ref={refs.setReference}
          className={`
            relative flex items-center
            ${sizeClasses[size]}
            ${fullWidth ? 'w-full' : 'w-auto'}
            border-0 rounded-md
            ${disabled ? 'bg-muted cursor-not-allowed' : 'bg-card-muted cursor-pointer'}
            ${error ? 'ring-1 ring-destructive' : ''}
            hover:ring-1 hover:ring-primary/20 focus-within:ring-2 focus-within:ring-primary/30
            transition-all
          `}
          {...getReferenceProps()}
        >
          {!hiddenInput && (
            <input
              ref={finalInputRef}
              type="text"
              value={inputValue}
              onChange={handleInputChange}
              placeholder={placeholder}
              disabled={disabled}
              className={`
                flex-1 bg-transparent px-3 py-2 text-foreground placeholder:text-muted-foreground
                focus:outline-none
                ${iconOnly ? 'sr-only' : ''}
                ${noBorder ? 'px-0' : ''}
              `}
              {...inputProps}
            />
          )}

          {/* Calendar Icon */}
          {showCalendarIcon && (
            <div className="flex items-center px-2">
              {calendarIcon || <Icon name="calendar" size="sm" className="text-muted-foreground" />}
            </div>
          )}

          {/* Clear Button */}
          {clearable && value && !disabled && (
            <button
              type="button"
              onClick={(e) => {
                e.stopPropagation();
                handleClear();
              }}
              className="flex items-center px-2 text-muted-foreground hover:text-foreground"
            >
              <Icon name="x" size="sm" />
            </button>
          )}
        </div>

        {/* Helper Text */}
        {helperText && !iconOnly && (
          <p className={`mt-1 text-xs ${error ? 'text-destructive' : 'text-muted-foreground'}`}>
            {helperText}
          </p>
        )}

        {/* Calendar + Time Picker dropdown */}
        {isOpen && (
          <FloatingPortal>
            <FloatingFocusManager context={context} modal={false} order={['reference', 'content']}>
              <div
                ref={refs.setFloating}
                style={floatingStyles}
                className="z-[9200] datepicker-dropdown shadow-lg rounded-lg overflow-hidden bg-card border-0"
                {...getFloatingProps()}
              >
                <div className="flex min-h-[400px]">
                  {/* Calendar */}
                  <div className="flex-1">
                    <Calendar
                      selectedDate={value}
                      onSelectDate={handleSelectDate}
                      month={calendarMonth}
                      onMonthChange={handleMonthChange}
                      disabledDates={disabledDates}
                      minDate={minDate}
                      maxDate={maxDate}
                      showToday={showToday}
                      showWeekNumbers={showWeekNumbers}
                      firstDayOfWeek={firstDayOfWeek}
                      weekDayNames={weekDayNames}
                      monthNames={monthNames}
                      showTodayButton={showTodayButton}
                      todayButtonText={todayButtonText}
                      noRightRadius={true}
                    />
                  </div>

                  {/* Time Picker */}
                  <div className="w-48 bg-card flex flex-col rounded-l-none">
                    <div className="text-sm font-medium text-foreground p-4 pb-2 text-center">
                      {t('common:time', 'Thời gian')}
                    </div>

                    <div className="flex-1 flex flex-col justify-center px-4 py-4">
                      <div className="flex justify-center gap-4">
                        {/* Hours Scroll Wheel */}
                        <TimeScrollWheel
                          value={selectedTime.hours}
                          onChange={(value) => handleTimeChange('hours', value)}
                          options={hours}
                          label={t('common:hours', 'Giờ')}
                          height={240}
                          itemHeight={32}
                          visibleItems={7}
                        />

                        {/* Minutes Scroll Wheel */}
                        <TimeScrollWheel
                          value={selectedTime.minutes}
                          onChange={(value) => handleTimeChange('minutes', value)}
                          options={minutes}
                          label={t('common:minutes', 'Phút')}
                          height={240}
                          itemHeight={32}
                          visibleItems={7}
                        />

                        {/* Seconds Scroll Wheel */}
                        {showSeconds && (
                          <TimeScrollWheel
                            value={selectedTime.seconds}
                            onChange={(value) => handleTimeChange('seconds', value)}
                            options={seconds}
                            label={t('common:seconds', 'Giây')}
                            height={240}
                            itemHeight={32}
                            visibleItems={7}
                          />
                        )}
                      </div>

                      {/* AM/PM for 12h format */}
                      {timeFormat === '12h' && (
                        <div className="mt-6 flex justify-center">
                          <div className="flex flex-col items-center">
                            <label className="block text-xs text-muted-foreground mb-2">
                              AM/PM
                            </label>
                            <div className="flex bg-card-muted rounded-md p-1">
                              <button
                                type="button"
                                onClick={() => {
                                  const newHours = selectedTime.hours % 12;
                                  handleTimeChange('hours', newHours);
                                }}
                                className={`px-3 py-1 text-xs rounded transition-colors ${
                                  selectedTime.hours < 12
                                    ? 'bg-primary text-primary-foreground'
                                    : 'text-muted-foreground hover:text-foreground'
                                }`}
                              >
                                AM
                              </button>
                              <button
                                type="button"
                                onClick={() => {
                                  const newHours = (selectedTime.hours % 12) + 12;
                                  handleTimeChange('hours', newHours);
                                }}
                                className={`px-3 py-1 text-xs rounded transition-colors ${
                                  selectedTime.hours >= 12
                                    ? 'bg-primary text-primary-foreground'
                                    : 'text-muted-foreground hover:text-foreground'
                                }`}
                              >
                                PM
                              </button>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Action Buttons */}
                    <div className="flex justify-between p-4 pt-3">
                      <button
                        type="button"
                        onClick={handleClear}
                        className="px-4 py-2 text-sm border border-border rounded hover:bg-muted transition-colors"
                      >
                        {clearButtonText || t('common:clear', 'Xóa')}
                      </button>
                      <button
                        type="button"
                        onClick={handleClose}
                        className="px-4 py-2 text-sm bg-primary text-primary-foreground rounded hover:bg-primary/90 transition-colors"
                      >
                        {t('common:done', 'Xong')}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </FloatingFocusManager>
          </FloatingPortal>
        )}
      </div>
    );
  }
);

DateTimePicker.displayName = 'DateTimePicker';

export default DateTimePicker;
