import React, { useState } from 'react';
import { SlideInForm, Typography, Button } from '@/shared/components/common';
import { CloudStorageProviderForm } from '../cloud-storage/components';
import type { CloudStorageProviderConfiguration } from '../cloud-storage/types';

/**
 * Trang quản lý tích hợp Cloud Storage
 */
const CloudStorageIntegrationPage: React.FC = () => {

  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingProvider, setEditingProvider] = useState<CloudStorageProviderConfiguration | null>(null);

  const handleCreateNew = () => {
    setEditingProvider(null);
    setShowCreateForm(true);
  };

  const handleEdit = (provider: CloudStorageProviderConfiguration) => {
    setEditingProvider(provider);
    setShowCreateForm(true);
  };

  const handleFormSuccess = () => {
    setShowCreateForm(false);
    setEditingProvider(null);
  };

  const handleFormCancel = () => {
    setShowCreateForm(false);
    setEditingProvider(null);
  };

  return (
    <div className="w-full bg-background text-foreground">
      {/* Main Content */}
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <Typography variant="h1">Cloud Storage Integration</Typography>
          <Button variant="primary" onClick={handleCreateNew}>
            Add Provider
          </Button>
        </div>

        {/* TODO: Implement CloudStorageProviderList component */}
        <div className="text-center py-8">
          <Typography variant="body1" className="text-muted-foreground">
            Cloud Storage Provider List component needs to be implemented
          </Typography>
        </div>
      </div>

      {/* Slide-in Form */}
      <SlideInForm
        isVisible={showCreateForm}
      >
        <CloudStorageProviderForm
          provider={editingProvider || undefined}
          onSuccess={handleFormSuccess}
          onCancel={handleFormCancel}
        />
      </SlideInForm>
    </div>
  );
};

export default CloudStorageIntegrationPage;
