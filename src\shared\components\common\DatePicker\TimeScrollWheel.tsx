import { useEffect, useRef, useState, useCallback } from 'react';

export interface TimeScrollWheelProps {
  /**
   * Gi<PERSON> trị hiện tại
   */
  value: number;
  
  /**
   * Callback khi giá trị thay đổi
   */
  onChange: (value: number) => void;
  
  /**
   * <PERSON>h sách các giá trị có thể chọn
   */
  options: number[];
  
  /**
   * Label hiển thị
   */
  label: string;
  
  /**
   * Chiều cao của container
   */
  height?: number;
  
  /**
   * Chiều cao của mỗi item
   */
  itemHeight?: number;
  
  /**
   * Số lượng items hiển thị
   */
  visibleItems?: number;
}

/**
 * Component scroll wheel để chọn thời gian
 */
const TimeScrollWheel: React.FC<TimeScrollWheelProps> = ({
  value,
  onChange,
  options,
  label,
  height = 120,
  itemHeight = 32,
  visibleItems = 3,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);

  // Tính toán scroll position dựa trên value
  const getScrollPosition = useCallback((targetValue: number) => {
    const index = options.findIndex(option => option === targetValue);
    if (index === -1) return 0;

    // Scroll để đưa item vào vị trí center
    // Vị trí thực tế của item trong paddedOptions là (paddingItems + index)
    // Để item nằm ở center (vị trí Math.floor(visibleItems / 2)), cần scroll:
    const targetPosition = paddingItems + index; // vị trí item trong paddedOptions
    const centerPosition = Math.floor(visibleItems / 2); // vị trí center trong viewport
    return (targetPosition - centerPosition) * itemHeight;
  }, [options, itemHeight, paddingItems, visibleItems]);

  // Tính toán value dựa trên scroll position
  const getValueFromScroll = useCallback((scrollTop: number) => {
    // Tính vị trí item ở center dựa trên scroll position
    const centerPosition = Math.floor(visibleItems / 2);
    const itemAtCenter = Math.round(scrollTop / itemHeight) + centerPosition;

    // Chuyển đổi về index trong options array (trừ đi padding)
    const optionIndex = itemAtCenter - paddingItems;
    const clampedIndex = Math.max(0, Math.min(optionIndex, options.length - 1));
    return options[clampedIndex];
  }, [options, itemHeight, paddingItems, visibleItems]);

  // Scroll to value
  const scrollToValue = useCallback((targetValue: number, smooth = true) => {
    if (!containerRef.current) return;

    const scrollPosition = getScrollPosition(targetValue);
    containerRef.current.scrollTo({
      top: scrollPosition,
      behavior: smooth ? 'smooth' : 'auto',
    });
  }, [getScrollPosition]);

  // Initialize scroll position
  useEffect(() => {
    scrollToValue(value, false);
  }, [value, scrollToValue]);

  // Handle scroll with debouncing for smooth snapping
  const [scrollTimeout, setScrollTimeout] = useState<NodeJS.Timeout | null>(null);

  const handleScroll = useCallback(() => {
    if (!containerRef.current) return;

    // Clear existing timeout
    if (scrollTimeout) {
      clearTimeout(scrollTimeout);
    }

    // Set new timeout for snapping
    const timeout = setTimeout(() => {
      if (containerRef.current && !isDragging) {
        const currentScrollTop = containerRef.current.scrollTop;
        const snapValue = getValueFromScroll(currentScrollTop);
        if (snapValue !== value) {
          onChange(snapValue);
          scrollToValue(snapValue, true);
        }
      }
    }, 150);

    setScrollTimeout(timeout);
  }, [value, onChange, getValueFromScroll, isDragging, scrollTimeout, scrollToValue]);

  // Handle wheel scroll for better UX
  const handleWheel = useCallback((e: React.WheelEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!containerRef.current) return;

    const delta = e.deltaY > 0 ? 1 : -1;
    const currentIndex = options.findIndex(option => option === value);
    const newIndex = Math.max(0, Math.min(currentIndex + delta, options.length - 1));
    const newValue = options[newIndex];

    if (newValue !== value) {
      onChange(newValue);
      scrollToValue(newValue, true);
    }
  }, [options, value, onChange, scrollToValue]);

  // Cleanup scroll timeout on unmount
  useEffect(() => {
    return () => {
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }
    };
  }, [scrollTimeout]);

  // Generate padding items for smooth scrolling
  const paddingItems = Math.floor(visibleItems / 2);
  const paddedOptions = [
    ...Array(paddingItems).fill(null),
    ...options,
    ...Array(paddingItems).fill(null),
  ];

  return (
    <div className="flex flex-col items-center">
      <label className="block text-xs text-muted-foreground mb-2 text-center">
        {label}
      </label>
      
      <div className="relative">
        {/* Center line indicator - vạch trắng cố định ở giữa */}
        <div
          className="absolute left-0 right-0 border-t border-white/60 pointer-events-none z-20"
          style={{
            top: `${Math.floor(visibleItems / 2) * itemHeight + itemHeight / 2}px`,
          }}
        />
        <div
          className="absolute left-0 right-0 border-b border-white/60 pointer-events-none z-20"
          style={{
            top: `${Math.floor(visibleItems / 2) * itemHeight + itemHeight / 2}px`,
          }}
        />
        
        {/* Scroll container */}
        <div
          ref={containerRef}
          className="overflow-y-auto overflow-x-hidden select-none custom-scrollbar invisible-scrollbar"
          style={{ height: `${height}px`, width: '60px' }}
          onScroll={handleScroll}
          onWheel={handleWheel}
        >
          <div className="flex flex-col">
            {paddedOptions.map((option, index) => {
              // Tính toán xem item này có phải là item được chọn không
              const isSelected = option === value;
              // Tính toán xem item này có nằm trong vùng center không
              const isInCenter = index >= paddingItems && index < paddingItems + options.length;

              return (
                <div
                  key={index}
                  className={`
                    flex items-center justify-center text-sm transition-all duration-200
                    ${isSelected ? 'text-foreground font-semibold scale-110' : 'text-muted-foreground'}
                    ${option === null ? 'opacity-0 pointer-events-none' : 'opacity-100 cursor-pointer hover:text-foreground hover:scale-105'}
                    leading-none
                  `}
                  style={{
                    height: `${itemHeight}px`,
                    lineHeight: `${itemHeight}px`
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    if (option !== null && isInCenter) {
                      onChange(option);
                      scrollToValue(option, true);
                    }
                  }}
                  onMouseDown={(e) => {
                    e.stopPropagation();
                  }}
                >
                  {option !== null ? option.toString().padStart(2, '0') : ''}
                </div>
              );
            })}
          </div>
        </div>
        
        {/* Fade gradients - tạo hiệu ứng mờ dần ở trên và dưới */}
        <div className="absolute top-0 left-0 right-0 h-12 bg-gradient-to-b from-card via-card/80 to-transparent pointer-events-none z-10" />
        <div className="absolute bottom-0 left-0 right-0 h-12 bg-gradient-to-t from-card via-card/80 to-transparent pointer-events-none z-10" />
      </div>
    </div>
  );
};

export default TimeScrollWheel;
