import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography } from '@/shared/components/common';
import { ProductTypeEnum } from '../../types/product.types';
import {
  OrderItemDto,
  ShippingDto,
  DigitalDeliveryDto,
  OrderCustomerDto
} from '../../types/order.types';
import PhysicalShippingForm from './PhysicalShippingForm';
import DigitalDeliveryForm from './DigitalDeliveryForm';
import ServiceDeliveryForm from './ServiceDeliveryForm';
import EventDeliveryForm from './EventDeliveryForm';

// Import types từ các form components
interface ServiceDeliveryInfo {
  serviceDate?: string;
  serviceTime?: string;
  serviceLocation?: string;
  serviceNotes?: string;
  contactPerson?: string;
  contactPhone?: string;
}

interface EventDeliveryInfo {
  eventDate?: string;
  eventTime?: string;
  eventLocation?: string;
  eventAddress?: string;
  ticketDeliveryMethod?: 'EMAIL' | 'SMS' | 'PICKUP' | 'MAIL';
  ticketRecipient?: string;
  eventNotes?: string;
  attendeeName?: string;
  attendeePhone?: string;
  attendeeEmail?: string;
}

interface DeliveryFormManagerProps {
  selectedItems: OrderItemDto[];
  selectedCustomer?: OrderCustomerDto;
  shipping?: ShippingDto;
  digitalDelivery?: DigitalDeliveryDto;
  onShippingChange: (shipping: ShippingDto) => void;
  onDigitalDeliveryChange: (delivery: DigitalDeliveryDto) => void;
  onServiceInfoChange?: (info: ServiceDeliveryInfo) => void;
  onEventInfoChange?: (info: EventDeliveryInfo) => void;
}

/**
 * Component quản lý các form giao hàng/vận chuyển dựa trên loại sản phẩm
 */
const DeliveryFormManager: React.FC<DeliveryFormManagerProps> = ({
  selectedItems,
  selectedCustomer,
  shipping,
  digitalDelivery,
  onShippingChange,
  onDigitalDeliveryChange,
  onServiceInfoChange,
  onEventInfoChange,
}) => {
  const { t } = useTranslation(['business', 'common']);

  // Phân tích loại sản phẩm trong đơn hàng
  const productTypeAnalysis = useMemo(() => {
    const types = new Set<ProductTypeEnum>();
    
    selectedItems.forEach(item => {
      if (item.productType) {
        types.add(item.productType);
      } else {
        // Mặc định là PHYSICAL nếu không có productType
        types.add(ProductTypeEnum.PHYSICAL);
      }
    });

    return {
      types: Array.from(types),
      hasPhysical: types.has(ProductTypeEnum.PHYSICAL),
      hasDigital: types.has(ProductTypeEnum.DIGITAL),
      hasService: types.has(ProductTypeEnum.SERVICE),
      hasEvent: types.has(ProductTypeEnum.EVENT),
      hasCombo: types.has(ProductTypeEnum.COMBO),
      isMultipleTypes: types.size > 1,
    };
  }, [selectedItems]);

  // Xác định combo có chứa sản phẩm vật lý không
  const comboContainsPhysical = useMemo(() => {
    if (!productTypeAnalysis.hasCombo) return false;
    
    // Logic để xác định combo có chứa sản phẩm vật lý
    // Hiện tại giả định combo luôn có thể chứa sản phẩm vật lý
    // Có thể cần API để lấy thông tin chi tiết về combo
    return true;
  }, [productTypeAnalysis.hasCombo]);

  // Xác định cần hiển thị form nào
  const shouldShowPhysicalShipping = productTypeAnalysis.hasPhysical || 
    (productTypeAnalysis.hasCombo && comboContainsPhysical);
  
  const shouldShowDigitalDelivery = productTypeAnalysis.hasDigital;
  const shouldShowServiceDelivery = productTypeAnalysis.hasService;
  const shouldShowEventDelivery = productTypeAnalysis.hasEvent;

  // Nếu không có sản phẩm nào được chọn
  if (selectedItems.length === 0) {
    return (
      <div className="text-center py-8">
        <Typography variant="body1" className="text-gray-500">
          {t('business:order.selectProductsFirst')}
        </Typography>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Hiển thị thông tin về loại sản phẩm */}
      {productTypeAnalysis.isMultipleTypes && (
        <div className="p-4 bg-blue-50 rounded-lg">
          <Typography variant="subtitle2" className="mb-2 text-blue-800">
            {t('business:order.multipleProductTypes')}
          </Typography>
          <Typography variant="body2" className="text-blue-600">
            {t('business:order.multipleProductTypesDescription')}
          </Typography>
        </div>
      )}

      {/* Form vận chuyển sản phẩm vật lý */}
      {shouldShowPhysicalShipping && (
        <PhysicalShippingForm
          shipping={shipping}
          onShippingChange={onShippingChange}
          customerAddress={selectedCustomer?.address}
        />
      )}

      {/* Form giao hàng sản phẩm số */}
      {shouldShowDigitalDelivery && (
        <DigitalDeliveryForm
          delivery={digitalDelivery}
          onDeliveryChange={onDigitalDeliveryChange}
          customerEmail={selectedCustomer?.email}
          customerPhone={selectedCustomer?.phone}
        />
      )}

      {/* Form dịch vụ */}
      {shouldShowServiceDelivery && onServiceInfoChange && (
        <ServiceDeliveryForm
          serviceInfo={undefined} // Sẽ cần thêm state cho service info
          onServiceInfoChange={onServiceInfoChange}
          customerName={selectedCustomer?.name}
          customerPhone={selectedCustomer?.phone}
        />
      )}

      {/* Form sự kiện */}
      {shouldShowEventDelivery && onEventInfoChange && (
        <EventDeliveryForm
          eventInfo={undefined} // Sẽ cần thêm state cho event info
          onEventInfoChange={onEventInfoChange}
          customerName={selectedCustomer?.name}
          customerPhone={selectedCustomer?.phone}
          customerEmail={selectedCustomer?.email}
        />
      )}

      {/* Thông tin tóm tắt */}
      <div className="p-4 bg-gray-50 rounded-lg">
        <Typography variant="subtitle2" className="mb-2">
          {t('business:order.deliverySummary')}
        </Typography>
        <div className="space-y-1">
          {shouldShowPhysicalShipping && (
            <Typography variant="body2" className="text-gray-600">
              • {t('business:order.physicalShippingRequired')}
            </Typography>
          )}
          {shouldShowDigitalDelivery && (
            <Typography variant="body2" className="text-gray-600">
              • {t('business:order.digitalDeliveryRequired')}
            </Typography>
          )}
          {shouldShowServiceDelivery && (
            <Typography variant="body2" className="text-gray-600">
              • {t('business:order.serviceDeliveryRequired')}
            </Typography>
          )}
          {shouldShowEventDelivery && (
            <Typography variant="body2" className="text-gray-600">
              • {t('business:order.eventDeliveryRequired')}
            </Typography>
          )}
        </div>
      </div>
    </div>
  );
};

export default DeliveryFormManager;
